<template>
	<view>
		<view>
			<image :src="info.mulu.url" style="width: 100%;height: 460rpx;;display: block;object-fit: cover;"></image>
		</view>
		<view style="padding: 30rpx;background-color: #ffffff;">
			<view style="display: flex;justify-content: space-between;align-items: center;">
				<view style="font-size: 34rpx;font-weight: 600">{{info.mulu.mulu_name}}</view>
				<view>
					<button open-type="share" style="font-size: 24rpx;color: #999999">转发</button>
				</view>
			</view>
		</view>
		<view style="margin-top: 15rpx;padding: 30rpx;background-color: #ffffff;">
			<tn-tabs :list="list" :isScroll="false" :current="current" name="name" :bold="true" activeColor="#1A73E8"
				inactiveColor="#999999" :fontSize="36" @change="change"></tn-tabs>
			<view style="height: 1px;background-color: #eeeeee;width: 100%;margin: 30rpx 0rpx;"></view>
			<view v-if="current==0">
				<view v-html="info.mulu.mulu_content"></view>
			</view>
			<view v-for="(item,index) in info.viedo" v-if="current==1" @click="openUrl(item)">
				<view style="display: flex;">
					<view style="font-size: 32rpx;font-weight: 600;color: #999999;">{{index<9?0:''}}{{index+1}}</view>
					<view style="width: 100%;padding-left: 20rpx;">
						<view style="display: flex;align-items: center;justify-content: space-between;">
							<view style="font-size: 30rpx;font-weight: 600;">{{item.viedo_name}}</view>
							<view>
								<text v-if="item.status==2"
									style="border: #1A73E8 solid 1px;color: #1A73E8;font-size: 24rpx;border-radius: 5rpx;">免费</text>
								<text v-if="item.status==1"
									style="border: #1A73E8 solid 1px;color: #1A73E8;font-size: 24rpx;border-radius: 5rpx;">会员</text>
							</view>
						</view>
						<view style="color: #999999;margin-top: 20rpx;">▶ 视频</view>
					</view>
				</view>
				<view style="height: 1px;background-color: #eeeeee;width: 100%;margin: 30rpx 0rpx;"></view>
			</view>
		</view>
	</view>
</template>

<script>
	import newsApi from "@/common/api/news.js"
	export default {
		data() {
			return {
				list: [{
					name: '简介'
				}, {
					name: '目录'
				}],
				current: 0,
				info: {},
				id: 0,
				vip: 0,
			}
		},
		onLoad(op) {
			this.id = op.id;
			this.getList();
			this.getIsUser();
		},
		onShareAppMessage() {
			return {
				title: this.info.mulu.mulu_name,
				path: '/pagesSubject/video/list?id=' + this.id,
			};
		},
		methods: {
			getIsUser() {
				newsApi.ifUserMember(this, {}).then(res => {
					//0 不是会员  
					console.log(res);
					this.vip = res.code;
				})
			},
			change(d) {
				this.current = d;
			},
			getList() {
				newsApi.getViedoInfo(this, {
					id: this.id
				}).then(res => {
					console.log(res);
					this.info = res.data;
				})
			},
			openUrl(item) {
				console.log(this.vip);
				if (this.vip == 0 && item.status == 1) {
					uni.showModal({
						title: '提示',
						content: '请联系客服，购买会员！',
						confirmText: '联系客服',
						success: (res) => {
							if (res.confirm) {
								uni.makePhoneCall({
									phoneNumber: '18903795988' //仅为示例
								});
							}
						}
					});
					return;
				}
				uni.navigateTo({
					url: "/pagesSubject/video/info?id=" + item.id
				})
			}
		}
	}
</script>

<style>
	button,
	button::after {
		margin: 0;
		padding: 0;
		border: none;
		outline: none;
		border-radius: 0;
		background: transparent;
	}
</style>