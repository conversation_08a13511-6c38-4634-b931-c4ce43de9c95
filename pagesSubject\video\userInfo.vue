<template>
	<view>
		<video :autoplay="true" :src="info.shipin_file_url" style="width: 100%;"></video>
		<view style="padding: 30rpx;background-color: #ffffff;">
			<view style="display: flex;justify-content: space-between;align-items: center;">
				<view style="font-size: 34rpx;font-weight: 600">{{info.viedo_name}}</view>
				<view style="color: #1A73E8;">分数：{{info.fenshu==''?'暂无分数':info.fenshu}}</view>
			</view>
		</view>
		<view style="padding: 30rpx;background-color: #ffffff;margin-top: 10rpx;">
			<view style="display: flex;justify-content: space-between;align-items: center;">
				<view style="font-size: 34rpx;font-weight: 600">评价</view>
			</view>
		</view>
		<view style="padding: 30rpx;background-color: #ffffff;margin-top: 5rpx;">
			<view>{{info.content==''?'暂无评价':info.content}}</view>
		</view>
	</view>
</template>

<script>
	import newsApi from "@/common/api/news.js"
	export default {
		data() {
			return {
				info: '',
				id: 0,
			}
		},
		onLoad(op) {
			this.id = op.id;
			this.getList();
		},
		methods: {
			getList() {
				newsApi.userViedoFind(this, {
					id: this.id
				}).then(res => {
					console.log(res);
					this.info = res.data;
				})
			},
		}
	}
</script>

<style>
	button,
	button::after {
		margin: 0;
		padding: 0;
		border: none;
		outline: none;
		border-radius: 0;
		background: transparent;
	}
</style>