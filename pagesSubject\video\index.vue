<template>
	<view>
		<view v-for="item in list">
			<view style="display: flex;justify-content: center;align-items: center;gap: 30rpx;margin: 30rpx 0rpx;">
				<view style="color: #999999;">——</view>
				<view style="font-size: 36rpx;font-weight: 600">{{item.type_name}}</view>
				<view style="color: #999999;">——</view>
			</view>
			<view style="background-color: #ffffff;">
				<view v-for="it in item.children"  @click="openUrl(it.id)">
					<view style="display: flex;padding: 30rpx;">
						<view>
							<image :src="it.myluimage"
								style="width: 270rpx;height: 160rpx;object-fit: cover;border-radius: 10rpx;"></image>
						</view>
						<view style="width: 100%;height: 160rpx;position: relative;">
							<view style="font-size: 30rpx;font-weight: 600;padding-left: 20rpx;">{{it.mulu_name}}</view>
						</view>
					</view>
					<view style="height: 1px;background-color: #eeeeee;width: 100%;"></view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import newsApi from "@/common/api/news.js"
	export default {
		data() {
			return {
				list: []
			}
		},
		onLoad() {
			this.getList();
		},
		methods: {
			getList() {
				newsApi.getViedoType(this, {}).then(res => {
					console.log(res);
					this.list = res.data;
				})
			},
			openUrl(id) {
				uni.navigateTo({
					url: "/pagesSubject/video/list?id="+id
				})
			}
		}
	}
</script>

<style>

</style>