<!-- 新闻 详情 -->
<template>
	<view class="container">
		<view>
			<view style="background-color: #FFFFFF;padding: 30rpx 30rpx 80rpx 30rpx;">
				<view class="contentBox text-lg text-black">
					<mp-html :tag-style="tagStyle" :scroll-table="true" :content="newsData.contents" />
				</view>

				<!-- 地图区域 -->
				<view class="mapSection margin-top-lg">
					<view class="mapTitle text-xl text-black text-bold margin-bottom-sm">
						<text class="cuIcon-locationfill text-blue margin-right-xs"></text>
						我们的位置
					</view>
					<view class="mapContainer">
						<map
							:latitude="mapData.latitude"
							:longitude="mapData.longitude"
							:markers="mapData.markers"
							:scale="mapData.scale"
							:show-location="true"
							@markertap="handleMarkerTap"
							@callouttap="handleCalloutTap"
							class="map"
						></map>
					</view>
				</view>
			</view>

			<!-- <view class="cu-bar justify-left bg-white margin-top-sm">
        <view class="action border-title">
          <text class="text-lg text-bold text-blue">图片展示</text>
          <text class="bg-gradual-blue" style="width:3rem"></text>
        </view>
      </view> -->
			<!-- <view style="background-color: #FFFFFF;padding: 0rpx 30rpx 30rpx 30rpx;">

        <image @click="previewImage(index)" v-for="(item, index) in newsData.images" :key="index"
          mode="widthFix" :src="item"></image>

        <view class="text-right text-df text-gray margin-top-sm margin-bottom-sm">
          <text class="text-gray cuIcon-attentionfill text-df" style="margin-right: 6rpx;"></text>
          <text class="text-df margin-right-sm" style="margin-top: 2rpx;">{{newsData.seeNum}}</text>
          <text class="text-gray cuIcon-appreciatefill text-df" style="margin-right: 6rpx;"></text>
          <text class="text-df margin-right-sm" style="margin-top: 2rpx;">{{newsData.likesNum}}</text>
          <text class="text-gray cuIcon-share text-df" style="margin-right: 6rpx;"></text>
          <text class="text-df" style="margin-top: 2rpx;">{{newsData.commentNum}}</text>
        </view>
      </view> -->
		</view>

		<!-- <view class="cu-bar bg-white tabbar border shop bottomBox">
      <view class="btn-group">
        <tn-button shape="round" backgroundColor="#1A73E8" padding="30rpx 0" width="200rpx" shadow @click="handleBack()"
                   fontBold plain>
          <text class="primary-color">返回列表</text>
        </tn-button>

        <tn-button shape="round" backgroundColor="#1A73E8" padding="30rpx 0" width="200rpx" shadow @click="handleGo()"
                   v-if="newsData && newsData.front_info && newsData.front_info.url" fontBold>
          <text class="tn-color-white">点击前往</text>
        </tn-button>
      </view>
    </view> -->
		<view class="safe-area-inset-bottom"></view>
	</view>
</template>

<script>
	import newsApi from "@/common/api/news.js"

	export default {
		data() {
			return {
				newsData: [],
				requestStatus: false, // 事件防抖
				tagStyle: {
					table: 'border: 1px solid gray;border-collapse:collapse',
					th: 'border: 1px solid gray;',
					td: 'border: 1px solid gray;'
				},
				// 地图数据
				mapData: {
					latitude: 34.621881, // 纬度
					longitude: 112.436631, // 经度
					scale: 17, // 地图缩放级别
					markers: [{
						id: 1,
						latitude: 34.621881,
						longitude: 112.436631,
						title: '名扬体育学院',
						width:15,
						height:20,
						callout: {
							content: '名扬体育学院',
							color: '#000000',
							fontSize: 14,
							borderRadius: 8,
							bgColor: '#ffffff',
							padding: 8,
							display: 'ALWAYS'
						}
					}]
				}
			}
		},
		onLoad(option) {
			console.log(option)
			this.getData(option.id);
		},
		methods: {
			// 获取数据
			getData(id) {
				console.log(id);

				newsApi.getNewsDetail(this, {
					id: id
				}).then(res => {
					if (res && res.data) {
						this.newsData = res.data
					}
				})
			},
			// 打开导航
			openNavigation() {
				uni.openLocation({
					latitude: this.mapData.latitude,
					longitude: this.mapData.longitude,
					name: '名扬体育学院',
					address: '名扬体育学院',
					scale: 18,
					success: () => {
						console.log('成功打开地图导航');
					},
					fail: (err) => {
						console.error('打开地图导航失败', err);
						uni.showToast({
							title: '打开地图失败',
							icon: 'none'
						});
					}
				});
			},
			// 处理地图标记点击事件
			handleMarkerTap(e) {
				console.log('标记点被点击', e);
				this.openNavigation();
			},
			// 处理callout点击事件
			handleCalloutTap(e) {
				console.log('callout被点击', e);
				this.openNavigation();
			},
			handleBack() {
				this.utils.goto('news-list')
			},
			handleGo() {
				if (this.newsData && this.newsData.front_info) {
					this.utils.goto(this.newsData.front_info.full_url)
				}
			},
			previewImage(index) {
				// 预览功能需要数组格式，具体查看uniapp文档：previewImage
				const seeImgList = this.newsData.images
				uni.previewImage({
					current: index, //预览图片的下标
					urls: seeImgList //预览图片的地址，必须要数组形式，如果不是数组形式就转换成数组形式就可以
				})
			},

		},
	}
</script>

<style lang="scss" scoped>
	button::after {
		border: none;
		background: transparent;
	}

	uni-button {
		background: transparent;
	}

	.container {
		background-color: #f2f2f2;
	}

	.solid {
		border-radius: 50rpx;
		text-indent: 12rpx;
	}

	image {
		width: 750rpx;
	}

	.bottomBox {
		width: 750rpx;
		position: fixed;
		left: 0;
		bottom: 0rpx;
	}

	/* 地图相关样式 */
	.mapSection {
		background-color: #FFFFFF;
		margin-top: 20rpx;
		border-radius: 16rpx;
	}

	.mapTitle {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.mapContainer {
		width: 100%;
		height: 400rpx;
		border-radius: 12rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	}

	.map {
		width: 100%;
		height: 100%;
	}
</style>