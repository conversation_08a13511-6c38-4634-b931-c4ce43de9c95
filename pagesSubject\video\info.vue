<template>
	<view>
		<video :autoplay="true" :src="info.url" style="width: 100%;"></video>
		<view style="padding: 30rpx;background-color: #ffffff;">
			<view style="display: flex;justify-content: space-between;align-items: center;">
				<view style="font-size: 34rpx;font-weight: 600">{{info.viedo_name}}</view>
				<view>
					<button open-type="share" style="font-size: 24rpx;color: #999999">转发</button>
				</view>
			</view>
		</view>
		<view style="padding: 30rpx;background-color: #ffffff;margin-top: 10rpx;">
			<view style="display: flex;justify-content: space-between;align-items: center;">
				<view style="font-size: 34rpx;font-weight: 600">视频详情</view>
			</view>
		</view>
		<view style="padding: 30rpx;background-color: #ffffff;margin-top: 5rpx;">
			<view v-html="info.viedo_content"></view>
		</view>
		<view v-if="info!=''" @click="updateVideo"
			style="position: fixed;left:0;right:0;border-radius:10rpx;bottom: 40rpx;;color: #ffffff;height: 80rpx;line-height: 80rpx;text-align: center;background-color: #1A73E8;width: 80%;margin: 0 auto">
			上传学习成果</view>
		<!-- 登录组件 -->
		<login ref="login" v-on:succ="ajax()"></login>
	</view>
</template>

<script>
	import newsApi from "@/common/api/news.js"
	export default {
		data() {
			return {
				info: '',
				id: 0,
			}
		},
		onLoad(op) {
			this.id = op.id;
			this.getList();
			
		},
		onShow() {
			uni.$on("login_success", (data) => {
				console.log("login_success", data);
				//this.user = data.user;
				this.getList();
			});
			if (!uni.getStorageSync("token")) {
				this.$nextTick(() => {
					//console.log(this.$refs.login);
					this.$refs.login.modal = true;
				});
			} else {

			}
		},
		onHide() {
			uni.$off("login_success");
		},
		onShareAppMessage() {
			return {
				title: this.info.viedo_name,
				path: '/pagesSubject/video/info?id=' + this.id,
			};
		},
		methods: {
			ajax() {

			},
			
			updateVideo() {
				if (!uni.getStorageSync("token")) {
					this.$refs.login.modal = true;
					return;
				}
				var that = this;
				uni.chooseMedia({
					count: 1,
					mediaType: ['video'],
					sourceType: ['album', 'camera'],
					success(item) {
						console.log(item);
						that.utils.uploadFile(that, item.tempFiles[0].tempFilePath).then((res) => {
							console.log(res);
							if (res.code == 1) {
								that.setVid(res.data.url);
							} else {
								uni.showToast({
									title: "视频上传失败，请刷新重试",
									icon: "error",
								});
							}
						});
					},
					fail(err) {
						console.log(err);
					}
				})

			},
			setVid(url) {
				newsApi.setViedo(this, {
					list_id: this.id,
					shipin_file: url
				}).then(res => {
					console.log(res);
					if (res.code == 1) {
						uni.showToast({
							title: "视频上传成功！",
							icon: "none",
						});
					} else {
						uni.showToast({
							title: "视频上传失败，请刷新重试",
							icon: "error",
						});
					}
				})
			},
			getList() {
				newsApi.getViedoSrc(this, {
					id: this.id
				}).then(res => {
					console.log(res);
					
					this.info = res.data;
				})
			},
			openUrl(id) {
				uni.navigateTo({
					url: "/pagesSubject/video/info?id=" + id
				})
			}
		}
	}
</script>

<style>
	button,
	button::after {
		margin: 0;
		padding: 0;
		border: none;
		outline: none;
		border-radius: 0;
		background: transparent;
	}
</style>