<template>
	<view>
		<view style="background-color: #ffffff;">
			<view v-for="item in list" @click="openUrl(item.id)">
				<view style="display: flex;padding: 30rpx;align-items: center;">
					<view style="width: 100%;position: relative;">
						<view style="font-size: 30rpx;font-weight: 600;padding-left: 20rpx;">{{item.viedo_name}}</view>
						<view
							style="padding-left: 20rpx;margin-top: 10rpx;">
							<view style="color: #1A73E8;">分数：{{item.fenshu==''?'暂无分数':item.fenshu}}</view>
							<view style="margin-top: 10rpx;">评价：{{item.content==''?'暂无评价':item.content}}</view>
						</view>
					</view>
					<view>
						<view style="background-color: #1A73E8;color: #ffffff;height: 60rpx;text-align: center;width: 150rpx;line-height: 60rpx">查看视频</view>
					</view>
				</view>
				<view style="height: 1px;background-color: #eeeeee;width: 100%;"></view>
			</view>
		</view>
	</view>
</template>

<script>
	import newsApi from "@/common/api/news.js"
	export default {
		data() {
			return {
				list:[],
				page:1,
			}
		},
		onLoad() {
			this.getList();
		},
		methods: {
			getList(){
				newsApi.userViedo(this, {page:this.page,size:10}).then(res => {
				  console.log(res);
				  this.list=res.data;
				})
			},
			openUrl(id) {
				uni.navigateTo({
					url:"/pagesSubject/video/userInfo?id="+id
				})
			}
		}
	}
</script>

<style>

</style>