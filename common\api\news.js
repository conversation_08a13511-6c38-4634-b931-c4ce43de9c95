import utils from "../js/utils.js";

/**
 * 学习动态相关接口
 */
module.exports = {

	/**
	 * 获取列表
	 * @returns {Promise<*>}
	 */
	getNewsList(handler, data = {}) {
		return utils.http(handler, 'news/index', data)
	},

	/**
	 * 获取详情
	 * @returns {Promise<*>}
	 */
	getNewsDetail(handler, data = {}) {
		return utils.http(handler, 'news/detail', data)
	},
	/**
	 * 视频分类
	 * @returns {Promise<*>}
	 */
	getViedoType(handler, data = {}) {
		return utils.http(handler, 'viedo', data)
	},
	/**
	 * 视频列表
	 * @returns {Promise<*>}
	 */
	getViedoInfo(handler, data = {}) {
		return utils.http(handler, 'viedo/muluFind', data)
	},
	/**
	 * 视频详情
	 * @returns {Promise<*>}
	 */
	getViedoSrc(handler, data = {}) {
		return utils.http(handler, 'viedo/viedo', data)
	},
	/**
	 * 上传
	 * @returns {Promise<*>}
	 */
	setViedo(handler, data = {}) {
		return utils.http(handler, 'viedo/submitVideo', data)
	},
	/**
	 * 查看我上传的列表
	 * @returns {Promise<*>}
	 */
	userViedo(handler, data = {}) {
		return utils.http(handler, 'viedo/jiluSelect', data)
	},
	/**
	 * 查看我上传的详情
	 * @returns {Promise<*>}
	 */
	userViedoFind(handler, data = {}) {
		return utils.http(handler, 'viedo/jilufind', data)
	},
	/**
	 * 查询是否是会员
	 * @returns {Promise<*>}
	 */
	ifUserMember(handler, data = {}) {
		return utils.http(handler, 'viedo/ifUserMember', data)
	},
}
